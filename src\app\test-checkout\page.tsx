'use client';

import React, { useState } from 'react';
import { useAuth } from '@/components/AuthProvider';
import { useRouter } from 'next/navigation';

const TestCheckoutPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();
  const router = useRouter();

  const handleTestCheckout = async () => {
    if (!user) {
      alert('Please log in first');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.uid,
          userEmail: user.email,
          amount: 2000, // $20.00
          productName: 'Test Embedded Checkout',
          productDescription: 'Testing the new embedded checkout functionality'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create checkout session');
      }

      const data = await response.json();
      
      if (data.clientSecret) {
        // Store the client secret and redirect to checkout page
        sessionStorage.setItem('stripe_client_secret', data.clientSecret);
        sessionStorage.setItem('stripe_session_id', data.sessionId);
        sessionStorage.setItem('stripe_transaction_id', data.transactionId);
        
        router.push('/checkout?amount=2000&productName=Test Embedded Checkout&productDescription=Testing the new embedded checkout functionality');
      } else {
        throw new Error('No client secret received');
      }
    } catch (error) {
      console.error('Error:', error);
      alert(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">Please Log In</h2>
          <p>You need to be logged in to test the checkout functionality.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-md mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold mb-4">Test Embedded Checkout</h1>
          <p className="text-gray-600 mb-6">
            Click the button below to test the new embedded Stripe checkout functionality.
          </p>
          
          <div className="border rounded-lg p-4 mb-6 bg-gray-50">
            <h3 className="font-semibold">Test Product</h3>
            <p className="text-sm text-gray-600">Testing the new embedded checkout functionality</p>
            <p className="text-lg font-bold mt-2">$20.00</p>
          </div>

          <button
            onClick={handleTestCheckout}
            disabled={loading}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Creating Checkout...' : 'Test Embedded Checkout'}
          </button>

          <div className="mt-4 text-sm text-gray-500">
            <p>Logged in as: {user.email}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestCheckoutPage;
